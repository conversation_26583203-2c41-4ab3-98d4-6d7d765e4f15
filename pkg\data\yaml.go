package data

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

// 角色定义
type Character struct {
	ID   string `yaml:"id"`
	Name string `yaml:"name"`
}

// 坐标位置（用于绝对定位）
type Position struct {
	X int `yaml:"x"`
	Y int `yaml:"y"`
}

// 移动偏移量（用于相对移动）
type MoveOffset struct {
	DX int `yaml:"dx"`
	DY int `yaml:"dy"`
}

// 动作接口（用于处理不同类型的动作）
type Action interface {
	GetType() string
}

// 说话动作
type SpeakAction struct {
	ActionType string   `yaml:"action"`
	Character  string   `yaml:"character"`
	Position   Position `yaml:"position"`
	Text       string   `yaml:"text"`
}

func (a SpeakAction) GetType() string { return "speak" }

// 移动动作（包含表达式解析）
type MoveAction struct {
	ActionType string     `yaml:"action"`
	Character  string     `yaml:"character"`
	Offset     MoveOffset `yaml:"position"` // 注意这里使用Offset而不是Position
	Duration   string     `yaml:"duration"` // 存储原始表达式字符串
}

func (a MoveAction) GetType() string { return "move" }

// 计算移动持续时间（解析表达式）
func (a MoveAction) CalculateDuration(frameRate int) (int, error) {
	// 替换表达式中的变量
	expr := strings.ReplaceAll(a.Duration, "${frame_rate}", strconv.Itoa(frameRate))

	// 简单表达式解析（实际项目中应使用表达式引擎）
	parts := strings.Split(expr, "*")
	if len(parts) != 2 {
		return 0, fmt.Errorf("invalid duration expression: %s", a.Duration)
	}

	val1, err := strconv.Atoi(strings.TrimSpace(parts[0]))
	if err != nil {
		return 0, err
	}

	val2, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil {
		return 0, err
	}

	return val1 * val2, nil
}

// 场景定义
type Scene struct {
	YamlPath   string      `yaml:"-"`
	Characters []Character `yaml:"characters"`
	SceneID    int         `yaml:"scene_id"`
	Background string      `yaml:"background"`
	FrameRate  int         `yaml:"frame_rate"`
	Actions    []Action    `yaml:"actions"` // 使用接口类型存储不同动作
}

// 自定义YAML解析器（处理不同类型的动作）
func (s *Scene) UnmarshalYAML(unmarshal func(interface{}) error) error {
	// 临时结构体用于解析通用字段
	type rawScene struct {
		Characters []Character `yaml:"characters"`
		SceneID    int         `yaml:"scene_id"`
		Background string      `yaml:"background"`
		FrameRate  int         `yaml:"frame_rate"`
		Actions    []yaml.Node `yaml:"actions"` // 使用原始节点处理
	}

	var raw rawScene
	if err := unmarshal(&raw); err != nil {
		return err
	}

	s.Characters = raw.Characters
	s.SceneID = raw.SceneID
	s.Background = raw.Background
	s.FrameRate = raw.FrameRate

	// 处理不同类型的动作
	for _, actionNode := range raw.Actions {
		var base struct {
			ActionType string `yaml:"action"`
		}
		if err := actionNode.Decode(&base); err != nil {
			return err
		}

		switch base.ActionType {
		case "speak":
			var speak SpeakAction
			if err := actionNode.Decode(&speak); err != nil {
				return err
			}
			s.Actions = append(s.Actions, speak)

		case "move":
			var move MoveAction
			if err := actionNode.Decode(&move); err != nil {
				return err
			}
			s.Actions = append(s.Actions, move)
		}
	}

	return nil
}

func ResolveSceneYaml(filePath string) (*Scene, error) {
	// 读取YAML文件内容
	yamlFile, err := os.ReadFile(filePath)
	if err != nil {
		log.Fatalf("读取文件失败: %v", err)
		return nil, err
	}

	// 解析YAML
	var scene Scene
	if err := yaml.Unmarshal(yamlFile, &scene); err != nil {
		log.Fatalf("解析YAML失败: %v", err)
		return nil, err
	}

	// 打印解析结果
	fmt.Println("=== 场景信息 ===")
	fmt.Printf("场景ID: %d\n", scene.SceneID)
	fmt.Printf("背景: %s\n", scene.Background)
	fmt.Printf("帧率: %d fps\n", scene.FrameRate)
	fmt.Println()

	fmt.Println("=== 角色列表 ===")
	for _, char := range scene.Characters {
		fmt.Printf("- %s (%s)\n", char.Name, char.ID)
	}
	fmt.Println()

	fmt.Println("=== 动作序列 ===")
	for i, action := range scene.Actions {
		fmt.Printf("动作 #%d [类型: %s]\n", i+1, action.GetType())

		switch a := action.(type) {
		case SpeakAction:
			fmt.Printf("  角色: %s\n", a.Character)
			fmt.Printf("  位置: (%d, %d)\n", a.Position.X, a.Position.Y)
			fmt.Printf("  台词: \"%s\"\n", a.Text)

		case MoveAction:
			fmt.Printf("  角色: %s\n", a.Character)
			fmt.Printf("  移动偏移: (dx: %d, dy: %d)\n", a.Offset.DX, a.Offset.DY)

			// 计算持续时间
			duration, err := a.CalculateDuration(scene.FrameRate)
			if err != nil {
				fmt.Printf("  错误计算持续时间: %v\n", err)
			} else {
				fmt.Printf("  持续时间: %d 帧 (%.2f 秒)\n",
					duration, float64(duration)/float64(scene.FrameRate))
			}
		}
		fmt.Println()
	}

	return &scene, nil
}
