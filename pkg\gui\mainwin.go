package gui

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"fyne.io/fyne/v2"
	"fyne.io/fyne/v2/app"
	"fyne.io/fyne/v2/dialog"
	"fyne.io/fyne/v2/driver/desktop"
	"fyne.io/fyne/v2/storage"
	"fyne.io/fyne/v2/theme"
)

var ()

var (
	appID     = "novel.tming"
	winTitile = "场景视频生成工具"

	mainMenuTitleFile   = "文件"
	sub1MenuTitleSelect = "选择场景yaml"

	mainMenuTitleHelp   = "帮助"
	sub1MenuTitleAbout  = "关于"
	sub1MenuTitleUpdate = "更新"
)

var (
	gMainWin fyne.Window
	gCanvas  fyne.CanvasObject

	gSelectedYamlPath  string
	defaultResourceDir = "resources"
)

// MainWin for main windows gui
func MainWin() {
	a := app.NewWithID(appID)
	// makeTray(a)

	gMainWin = a.NewWindow(winTitile)
	gMainWin.Resize(fyne.NewSize(500, 500))

	gMainWin.SetMainMenu(makeMenu(gMainWin))
	gMainWin.SetMaster()

	// gMainWin.SetContent(container.NewStack(NewCanvas(gMainWin)))
	gMainWin.SetContent(NewCanvas(gMainWin))

	// gMainWin.Resize(fyne.NewSize(500, 500))
	gMainWin.ShowAndRun()
}

func makeMenu(w fyne.Window) *fyne.MainMenu {
	// newItem := fyne.NewMenuItem("new", nil)

	helpMenu := fyne.NewMenu(mainMenuTitleHelp,
		fyne.NewMenuItem(sub1MenuTitleAbout, func() {
			fmt.Printf("Help->About")
		}),
		fyne.NewMenuItem(sub1MenuTitleUpdate, func() {
			fmt.Printf("Help->Update")
		}))

	file := fyne.NewMenu(mainMenuTitleFile,
		fyne.NewMenuItem(sub1MenuTitleSelect, selectYamlFile))

	main := fyne.NewMainMenu(
		file,
		helpMenu,
	)

	return main
}

func makeTray(a fyne.App) {
	if desk, ok := a.(desktop.App); ok {
		h := fyne.NewMenuItem("Hello", func() {})
		h.Icon = theme.HomeIcon()
		menu := fyne.NewMenu("Hello World", h)
		h.Action = func() {
			log.Println("System tray menu tapped")
			h.Label = "Welcome"
			menu.Refresh()
		}
		desk.SetSystemTrayMenu(menu)
	}
}

func selectYamlFile() {
	// 创建文件打开对话框
	fileDialog := dialog.NewFileOpen(func(reader fyne.URIReadCloser, err error) {
		if err != nil {
			dialog.ShowError(err, gMainWin)
			return
		}
		if reader == nil {
			log.Println("取消选择")
			return
		}

		// 获取文件路径
		gSelectedYamlPath = reader.URI().Path()
		fmt.Println("选择的文件路径:", gSelectedYamlPath)

		// 关闭文件
		_ = reader.Close()
	}, gMainWin)

	// 设置可接受的文件类型（可选）
	filter := storage.NewExtensionFileFilter([]string{".yaml"})
	fileDialog.SetFilter(filter)

	// 指定默认路径（例如："/home/<USER>/Documents"）
	defaultPath := ""
	curdir, err := os.Getwd()
	if err == nil {
		defaultPath = filepath.Join(curdir, defaultResourceDir)

		uri, err := storage.ParseURI("file://" + defaultPath)
		if err != nil {
			// 处理路径解析错误
			return
		}
		listableURI, err := storage.ListerForURI(uri)
		if err != nil {
			// 处理不可列出的 URI 错误
			return
		}

		fileDialog.SetLocation(listableURI)
	}

	fileDialog.Show()
}
